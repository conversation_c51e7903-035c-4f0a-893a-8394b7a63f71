import os
import logging
import json
import fitz  # PyMuPDF
import uuid
import base64
import numpy as np
from pathlib import Path
from datetime import datetime
from werkzeug.utils import secure_filename
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
import shutil
from app.services import vision_processor
import re
import time
import hashlib
from urllib.parse import urlparse
from typing import List, Dict, Any

# Import performance monitoring decorators
from app.utils.performance_monitor import (
    monitor_pdf_processing,
    performance_monitor,
    get_performance_monitor
)

# Import batch processing
from app.utils.batch_processor import get_batch_processor, BatchJob, batch_process_documents

# Import vision processor for image analysis
try:
    import vision_processor
    HAS_VISION = True
except ImportError:
    HAS_VISION = False
    logging.warning("Vision processor module not available. Image analysis features will be disabled.")

# Try to import optional dependencies
try:
    import cv2
    HAS_OPENCV = True
except ImportError:
    HAS_OPENCV = False
    logging.warning("opencv-python not available. Image processing features will be disabled.")

try:
    import pytesseract
    from PIL import Image
    HAS_OCR = True
except ImportError:
    HAS_OCR = False
    logging.warning("OCR dependencies (pytesseract, pillow) not available. OCR features will be disabled.")
    HAS_OCR = False
    HAS_OPENCV = False
    logging.warning("OCR dependencies (pytesseract, opencv-python, pillow) not available. OCR features will be disabled.")

try:
    import tabula
    HAS_TABULA = True
except ImportError:
    HAS_TABULA = False
    logging.warning("tabula-py not available. Table extraction with tabula will be disabled.")

try:
    import camelot
    HAS_CAMELOT = True
except ImportError:
    HAS_CAMELOT = False
    logging.warning("camelot-py not available. Table extraction with camelot will be disabled.")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./data/temp")
GHOSTSCRIPT_PATH = os.getenv("GHOSTSCRIPT_PATH", "D:/Program Files/gs/gs10.05.1/bin/gswin64c.exe")

# Import the directory creation function
from scripts.setup.create_temp_dirs import create_pdf_directory_structure

# Set Ghostscript path for pdf2image if it exists
if os.path.exists(GHOSTSCRIPT_PATH):
    os.environ["GHOSTSCRIPT_BINARY"] = GHOSTSCRIPT_PATH
else:
    logger.warning(f"Ghostscript not found at {GHOSTSCRIPT_PATH}. PDF to image conversion may be limited.")

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
def convert_ocr_to_non_ocr_pdf(input_pdf_path, output_pdf_path, dpi=300):
    """
    Convert OCR PDF to non-OCR PDF by extracting pages as images 
    and creating a new image-only PDF.
    
    This function removes all text layers from an OCR PDF by converting each page
    to a high-resolution image and creating a new PDF from these images.
    
    Args:
        input_pdf_path (str): Path to original OCR PDF
        output_pdf_path (str): Path where non-OCR PDF will be saved
        dpi (int): Resolution for image extraction (default: 300 DPI)
    
    Returns:
        tuple: (success, message, metadata)
    """
    try:
        logger.info(f"Starting OCR to non-OCR conversion: {input_pdf_path}")
        
        # Validate input file
        if not os.path.exists(input_pdf_path):
            return False, f"Input PDF not found: {input_pdf_path}", {}
        
        # Open the original PDF
        doc = fitz.open(input_pdf_path)
        page_count = len(doc)
        logger.info(f"Processing {page_count} pages from OCR PDF")
        
        # Create a new PDF document
        new_doc = fitz.open()
        
        # Calculate scale factor for DPI
        scale_factor = dpi / 72.0  # 72 DPI is default
        matrix = fitz.Matrix(scale_factor, scale_factor)
        
        # Track conversion metadata
        total_file_size = 0
        pages_converted = 0
        
        # Determine image format and quality based on DPI
        if dpi <= 150:
            # Low DPI: Use JPEG with high compression for smaller files
            img_format = "jpeg"
            jpeg_quality = 85
        elif dpi <= 300:
            # Medium DPI: Use JPEG with medium compression
            img_format = "jpeg"
            jpeg_quality = 95
        else:
            # High DPI: Use PNG for best quality
            img_format = "png"
            jpeg_quality = None
        
        logger.info(f"Using {img_format} format with {'quality ' + str(jpeg_quality) if jpeg_quality else 'lossless compression'}")
        
        # Process each page
        for page_num, page in enumerate(doc):
            try:
                logger.info(f"Converting page {page_num + 1}/{page_count}")
                
                # Get page dimensions
                page_rect = page.rect
                
                # Convert page to high-resolution image
                pix = page.get_pixmap(matrix=matrix)
                
                # Convert pixmap to appropriate format
                if img_format == "jpeg":
                    img_data = pix.tobytes("jpeg", jpg_quality=jpeg_quality)
                else:
                    img_data = pix.tobytes("png")
                
                total_file_size += len(img_data)
                
                # Create a new page in the output PDF with same dimensions
                new_page = new_doc.new_page(width=page_rect.width, height=page_rect.height)
                
                # Insert the image into the new page
                new_page.insert_image(new_page.rect, stream=img_data)
                
                pages_converted += 1
                logger.info(f"Successfully converted page {page_num + 1}")
                
            except Exception as e:
                logger.error(f"Error converting page {page_num + 1}: {str(e)}")
                # Continue with other pages even if one fails
                continue
        
        # Save the new non-OCR PDF
        logger.info(f"Saving non-OCR PDF to: {output_pdf_path}")
        new_doc.save(output_pdf_path)
        
        # Get final file size
        final_file_size = os.path.getsize(output_pdf_path)
        
        # Clean up
        new_doc.close()
        doc.close()
        
        # Create metadata
        metadata = {
            "original_pages": page_count,
            "pages_converted": pages_converted,
            "dpi": dpi,
            "scale_factor": scale_factor,
            "image_format": img_format,
            "jpeg_quality": jpeg_quality if jpeg_quality else "N/A",
            "original_file_size": os.path.getsize(input_pdf_path),
            "converted_file_size": final_file_size,
            "compression_ratio": final_file_size / os.path.getsize(input_pdf_path) if os.path.getsize(input_pdf_path) > 0 else 0,
            "size_reduction": ((os.path.getsize(input_pdf_path) - final_file_size) / os.path.getsize(input_pdf_path)) * 100 if os.path.getsize(input_pdf_path) > 0 else 0
        }
        
        success_message = f"Successfully converted {pages_converted}/{page_count} pages to non-OCR format"
        logger.info(success_message)
        logger.info(f"File size: {os.path.getsize(input_pdf_path)} bytes → {final_file_size} bytes")
        
        return True, success_message, metadata
        
    except Exception as e:
        error_message = f"Error converting PDF to non-OCR format: {str(e)}"
        logger.error(error_message)
        return False, error_message, {}

def detect_ocr_pdf(pdf_path):
    """
    Detect if a PDF contains OCR text layers by analyzing text extraction results.
    
    Args:
        pdf_path (str): Path to PDF file
        
    Returns:
        dict: Detection results with confidence score
    """
    try:
        doc = fitz.open(pdf_path)
        
        # Sample first few pages for analysis
        sample_pages = min(3, len(doc))
        total_text_length = 0
        pages_with_text = 0
        
        for page_num in range(sample_pages):
            page = doc[page_num]
            text = page.get_text()
            
            if text.strip():
                total_text_length += len(text)
                pages_with_text += 1
        
        doc.close()
        
        # Calculate metrics
        avg_text_per_page = total_text_length / sample_pages if sample_pages > 0 else 0
        text_coverage = pages_with_text / sample_pages if sample_pages > 0 else 0
        
        # Determine if likely OCR based on text patterns
        likely_ocr = False
        confidence = 0.0
        
        if avg_text_per_page > 500 and text_coverage > 0.5:
            likely_ocr = True
            confidence = min(0.9, text_coverage * (avg_text_per_page / 1000))
        
        return {
            "is_ocr_pdf": likely_ocr,
            "confidence": confidence,
            "avg_text_per_page": avg_text_per_page,
            "text_coverage": text_coverage,
            "pages_analyzed": sample_pages,
            "total_text_length": total_text_length
        }
        
    except Exception as e:
        logger.error(f"Error detecting OCR in PDF {pdf_path}: {str(e)}")
        return {
            "is_ocr_pdf": False,
            "confidence": 0.0,
            "error": str(e)
        }

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
def extract_text_standard(pdf_path):
    """Extract text from standard PDF using PyMuPDF."""
    text_by_page = []
    try:
        logger.info(f"Opening PDF for text extraction: {pdf_path}")
        doc = fitz.open(pdf_path)
        logger.info(f"PDF opened successfully. Number of pages: {len(doc)}")
        
        for page_num, page in enumerate(doc):
            logger.info(f"Processing page {page_num + 1}")
            text = page.get_text()
            logger.info(f"Page {page_num + 1} text length: {len(text)} characters")
            
            if text.strip():
                text_by_page.append({
                    "page": page_num + 1,
                    "text": text,
                    "extraction_method": "standard"
                })
                logger.info(f"Added text from page {page_num + 1}")
            else:
                logger.warning(f"No text extracted from page {page_num + 1}")
        
        logger.info(f"Text extraction completed. Total pages with text: {len(text_by_page)}")
        return text_by_page
    except Exception as e:
        logger.error(f"Failed to extract standard text from PDF {pdf_path}: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")
        return []

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
def extract_text_with_ocr(pdf_path):
    """Extract text from scanned PDF pages using Tesseract OCR."""
    text_by_page = []

    # Check if OCR dependencies are available
    if not HAS_OCR or not HAS_OPENCV:
        logger.warning("OCR dependencies not available. Skipping OCR text extraction.")
        return text_by_page

    try:
        # Open the PDF
        doc = fitz.open(pdf_path)

        for page_num, page in enumerate(doc):
            # Convert page to image
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better OCR
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

            # Convert PIL image to OpenCV format
            img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

            # Preprocess image for better OCR
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            gray = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)[1]

            # Apply OCR
            text = pytesseract.image_to_string(gray)

            if text.strip():
                text_by_page.append({
                    "page": page_num + 1,
                    "text": text,
                    "extraction_method": "ocr"
                })

        return text_by_page
    except Exception as e:
        logger.error(f"Failed to extract text with OCR from PDF {pdf_path}: {str(e)}")
        return []

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
def extract_images_from_pdf(pdf_path, category=None, save_images=True, use_vision=None, filter_sensitivity=None, max_images=None):
    """
    Extract and optionally save images from PDF using PyMuPDF with vision model analysis.

    This function extracts images from PDFs and saves them in the hierarchical directory structure
    (data/temp/CATEGORY/PDF_NAME/pdf_images/) when category is provided and save_images is True.

    Args:
        pdf_path: Path to the PDF file
        category: Category for organizing content
        save_images: Whether to save extracted images to disk (set to False to only extract metadata)
        use_vision: Whether to use vision model for image analysis (defaults to environment variable)
        filter_sensitivity: Sensitivity level for filtering images (low, medium, high)
        max_images: Maximum number of images to save

    Returns:
        List of image info dictionaries
    """
    images = []
    filtered_images = []

    # Get vision settings from environment variables if not provided
    if use_vision is None:
        use_vision = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'

    if filter_sensitivity is None:
        filter_sensitivity = os.getenv('PDF_IMAGE_FILTER_SENSITIVITY', 'medium')

    if max_images is None:
        try:
            max_images = int(os.getenv('MAX_PDF_IMAGES_TO_ANALYZE', '10'))
        except (ValueError, TypeError):
            max_images = 10

    # Set relevance threshold based on sensitivity
    if filter_sensitivity == 'low':
        relevance_threshold = 3  # More permissive
    elif filter_sensitivity == 'high':
        relevance_threshold = 7  # More strict
    else:  # medium (default)
        relevance_threshold = 5

    try:
        # Get the PDF filename
        pdf_name = os.path.basename(pdf_path)
        pdf_base_name = os.path.splitext(pdf_name)[0]

        # Determine the image directory path
        if save_images:
            if category:
                # Use the hierarchical directory structure (preferred method)
                dir_structure = create_pdf_directory_structure(category, pdf_name)
                if not dir_structure:
                    logger.error(f"Failed to create directory structure for {pdf_name}")
                    return []

                # Use the PDF-specific images directory
                image_dir = dir_structure["pdf_images_dir"]
            else:
                # Only use temp_images as a fallback if no category is provided
                # This should be rare in production use
                image_dir = os.path.join(TEMP_FOLDER, "temp_images")
                os.makedirs(image_dir, exist_ok=True)
                logger.warning(f"No category provided for PDF {pdf_name}, using temporary directory for images")

        # Open the PDF
        doc = fitz.open(pdf_path)

        # Track total images found for analytics
        total_images_found = 0
        images_saved = 0

        for page_num, page in enumerate(doc):
            image_list = page.get_images(full=True)

            for img_index, img in enumerate(image_list):
                total_images_found += 1

                xref = img[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                image_ext = base_image["ext"]

                # Create image info dictionary
                image_info = {
                    "page": page_num + 1,
                    "index": img_index,
                    "size": len(image_bytes),
                    "format": image_ext
                }

                # Process and save the image if requested
                if save_images:
                    # Generate a unique filename
                    image_filename = f"{pdf_base_name}_{page_num+1}_{img_index}.{image_ext}"
                    image_path = os.path.join(image_dir, image_filename)

                    with open(image_path, "wb") as f:
                        f.write(image_bytes)

                    # Add URL for accessing the image through the Flask app
                    if category:
                        image_info["url"] = f"/{category}/{pdf_base_name}/pdf_images/{image_filename}"
                    else:
                        image_info["url"] = f"/temp_images/{image_filename}"

                    # Add a description for better accessibility
                    image_info["description"] = f"Image from page {page_num+1} of {Path(pdf_path).name}"

                    # Analyze image with vision model if enabled and available
                    if use_vision and HAS_VISION:
                        try:
                            # Use the direct file path to avoid duplicate downloads and caching
                            # This prevents the vision processor from creating duplicate cached copies

                            # Store the file path in the image info for direct access
                            image_info["file_path"] = image_path

                            # Analyze the image using the direct file path
                            analysis = vision_processor.detect_image_content(image_path, is_pdf_image=True)

                            if "error" not in analysis:
                                # Add content metadata
                                image_info["is_logo"] = analysis.get("is_logo", False)
                                image_info["category"] = analysis.get("category", "unknown")
                                image_info["objects"] = analysis.get("objects", [])
                                image_info["has_text"] = analysis.get("text") is not None and len(analysis.get("text", "")) > 0
                                image_info["text_content"] = analysis.get("text")
                                image_info["is_decorative"] = analysis.get("is_decorative", False)
                                image_info["relevance_score"] = analysis.get("relevance_score", 5)

                                # Use the AI-generated description if available
                                if analysis.get("description"):
                                    image_info["description"] = analysis.get("description")

                                # Mark as analyzed
                                image_info["analyzed"] = True

                                # Check if image should be filtered based on relevance score
                                if (image_info.get("relevance_score", 5) < relevance_threshold or
                                    image_info.get("is_logo", False) or
                                    image_info.get("is_decorative", False)):

                                    # Add to filtered images list
                                    filtered_images.append(image_info)

                                    # Skip adding to main images list
                                    continue
                            else:
                                # Store error information
                                image_info["vision_error"] = analysis.get("error")
                                image_info["analyzed"] = False
                        except Exception as e:
                            logger.error(f"Error analyzing PDF image: {str(e)}")
                            image_info["vision_error"] = str(e)
                            image_info["analyzed"] = False

                # Add to images list if it passed filtering or if vision analysis is disabled
                # Always add basic metadata even if save_images is False
                images.append(image_info)
                images_saved += 1

                # Stop processing if we've reached the maximum number of images
                if max_images and images_saved >= max_images:
                    logger.info(f"Reached maximum number of images to save ({max_images})")
                    break

            # Also break outer loop if we've reached the maximum
            if max_images and images_saved >= max_images:
                break

        # Add analytics data to the first image if any images were saved
        if images:
            images[0]["total_images_found"] = total_images_found
            images[0]["images_saved"] = images_saved
            images[0]["images_filtered"] = len(filtered_images)
            images[0]["filter_sensitivity"] = filter_sensitivity
            images[0]["relevance_threshold"] = relevance_threshold

        # Add filtered images metadata for tracking
        if filtered_images:
            # Store filtered images separately for potential review
            filtered_dir = os.path.join(image_dir, "filtered")
            os.makedirs(filtered_dir, exist_ok=True)

            # Save filtered images metadata
            filtered_metadata_path = os.path.join(filtered_dir, "filtered_images.json")
            with open(filtered_metadata_path, "w") as f:
                json.dump(filtered_images, f, indent=2)

            logger.info(f"Filtered out {len(filtered_images)} images based on vision analysis")

        return images
    except Exception as e:
        logger.error(f"Failed to extract images from PDF {pdf_path}: {str(e)}")
        return []

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
def extract_tables_with_tabula(pdf_path, category=None, save_tables=True):
    """Extract tables from PDF using tabula-py."""
    tables = []

    # Check if tabula is available
    if not HAS_TABULA:
        logger.warning("tabula-py not available. Skipping table extraction with tabula.")
        return tables

    try:
        # Get the PDF filename
        pdf_name = os.path.basename(pdf_path)
        pdf_base_name = os.path.splitext(pdf_name)[0]

        # Determine the table directory path
        if save_tables:
            if category:
                # Use the hierarchical directory structure (preferred method)
                dir_structure = create_pdf_directory_structure(category, pdf_name)
                if not dir_structure:
                    logger.error(f"Failed to create directory structure for {pdf_name}")
                    return []

                # Use the PDF-specific tables directory
                table_dir = dir_structure["pdf_tables_dir"]
            else:
                # Only use temp_tables as a fallback if no category is provided
                # This should be rare in production use
                table_dir = os.path.join(TEMP_FOLDER, "temp_tables")
                os.makedirs(table_dir, exist_ok=True)
                logger.warning(f"No category provided for PDF {pdf_name}, using temporary directory for tables")

        # Extract all tables from the PDF
        extracted_tables = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True)

        if not extracted_tables:
            return []

        for i, table in enumerate(extracted_tables):
            # Convert table to HTML with better styling
            table_html = table.to_html(classes='table table-sm table-bordered table-responsive')

            # Enhance the HTML with better styling
            table_html = table_html.replace('<table', '<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;"')
            table_html = table_html.replace('<th', '<th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"')
            table_html = table_html.replace('<td', '<td style="border: 1px solid #dee2e6; padding: 0.3rem;"')

            # Save table if requested
            if save_tables:
                table_filename = f"{pdf_base_name}_{i+1}.html"
                table_path = os.path.join(table_dir, table_filename)

                with open(table_path, "w", encoding="utf-8") as f:
                    f.write(table_html)

                # Create table info
                table_info = {
                    "index": i + 1,
                    "rows": len(table),
                    "columns": len(table.columns),
                    "html": table_html
                }

                # Add URL for accessing the table through the Flask app
                # The URL format needs to be updated to match the new directory structure
                if category:
                    table_info["url"] = f"/{category}/{pdf_base_name}/pdf_tables/{table_filename}"
                else:
                    table_info["url"] = f"/temp_tables/{table_filename}"

                tables.append(table_info)

        return tables
    except Exception as e:
        logger.error(f"Failed to extract tables with tabula from PDF {pdf_path}: {str(e)}")
        return []

def extract_cover_image_from_pdf(pdf_path, category=None):
    """
    Extract the first page of a PDF as a cover image/thumbnail.

    Args:
        pdf_path: Path to the PDF file
        category: Category for organizing content

    Returns:
        Dictionary containing thumbnail information or None if extraction fails
    """
    try:
        # Get the PDF filename
        pdf_name = os.path.basename(pdf_path)
        pdf_base_name = os.path.splitext(pdf_name)[0]

        # Create directory structure if category is provided
        if category:
            dir_structure = create_pdf_directory_structure(category, pdf_name)
            if not dir_structure:
                logger.error(f"Failed to create directory structure for {pdf_name}")
                return None

            # Create cover_image directory
            pdf_images_dir = dir_structure["pdf_images_dir"]
            cover_image_dir = os.path.join(pdf_images_dir, "cover_image")
            os.makedirs(cover_image_dir, exist_ok=True)

            # Generate thumbnail filename
            thumbnail_filename = f"{pdf_base_name}_thumbnail.jpg"
            thumbnail_path = os.path.join(cover_image_dir, thumbnail_filename)

            # Open the PDF
            doc = fitz.open(pdf_path)

            # Check if PDF has pages
            if doc.page_count == 0:
                logger.warning(f"PDF {pdf_path} has no pages")
                return None

            # Get the first page
            page = doc[0]

            # Render the page to an image (higher resolution for better quality)
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))

            # Save the image
            pix.save(thumbnail_path)

            # Create thumbnail info
            thumbnail_info = {
                "source": "pdf_first_page",
                "path": thumbnail_path,
                "url": f"/{category}/{pdf_base_name}/pdf_images/cover_image/{thumbnail_filename}",
                "filename": thumbnail_filename,
                "description": f"Cover image from {pdf_name}"
            }

            # Log the thumbnail path for debugging
            logger.info(f"Created thumbnail at path: {thumbnail_path}")

            # Log the thumbnail URL for debugging
            logger.info(f"Created thumbnail with URL: {thumbnail_info['url']}")

            logger.info(f"Successfully extracted cover image from {pdf_path}")
            return thumbnail_info
        else:
            logger.warning(f"No category provided for PDF {pdf_name}, skipping cover image extraction")
            return None
    except Exception as e:
        logger.error(f"Failed to extract cover image from PDF {pdf_path}: {str(e)}")
        return None

def extract_links_from_pdf(pdf_path):
    """Extract links from PDF using PyMuPDF."""
    links = []
    try:
        doc = fitz.open(pdf_path)
        for page_num, page in enumerate(doc):
            link_list = page.get_links()
            for link in link_list:
                if "uri" in link:
                    uri = link["uri"]
                    if uri.startswith(("http://", "https://")):
                        link_info = {
                            "url": uri,
                            "page": page_num + 1
                        }
                        links.append(link_info)
        return links
    except Exception as e:
        logger.error(f"Failed to extract links from PDF {pdf_path}: {str(e)}")
        return []

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
def extract_tables_with_camelot(pdf_path, category=None, save_tables=True):
    """Extract tables from PDF using camelot-py (more accurate than tabula)."""
    tables = []

    # Check if camelot is available
    if not HAS_CAMELOT:
        logger.warning("camelot-py not available. Skipping table extraction with camelot.")
        return tables

    try:
        # Get the PDF filename
        pdf_name = os.path.basename(pdf_path)
        pdf_base_name = os.path.splitext(pdf_name)[0]

        # Determine the table directory path
        if save_tables:
            if category:
                # Use the hierarchical directory structure (preferred method)
                dir_structure = create_pdf_directory_structure(category, pdf_name)
                if not dir_structure:
                    logger.error(f"Failed to create directory structure for {pdf_name}")
                    return []

                # Use the PDF-specific tables directory
                table_dir = dir_structure["pdf_tables_dir"]
            else:
                # Only use temp_tables as a fallback if no category is provided
                # This should be rare in production use
                table_dir = os.path.join(TEMP_FOLDER, "temp_tables")
                os.makedirs(table_dir, exist_ok=True)
                logger.warning(f"No category provided for PDF {pdf_name}, using temporary directory for tables")

        # Extract tables from the PDF
        # Use lattice mode for tables with lines/borders
        lattice_tables = camelot.read_pdf(pdf_path, pages='all', flavor='lattice')
        # Use stream mode for tables without clear borders
        stream_tables = camelot.read_pdf(pdf_path, pages='all', flavor='stream')

        table_count = 0

        # Process lattice tables
        for i, table in enumerate(lattice_tables):
            if table.df.empty:
                continue

            table_count += 1
            # Convert table to HTML with better styling
            table_html = table.df.to_html(classes='table table-sm table-bordered table-responsive')

            # Enhance the HTML with better styling
            table_html = table_html.replace('<table', '<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;"')
            table_html = table_html.replace('<th', '<th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"')
            table_html = table_html.replace('<td', '<td style="border: 1px solid #dee2e6; padding: 0.3rem;"')

            if save_tables:
                table_filename = f"{pdf_base_name}_lattice_{i+1}.html"
                table_path = os.path.join(table_dir, table_filename)

                with open(table_path, "w", encoding="utf-8") as f:
                    f.write(table_html)

                table_info = {
                    "index": table_count,
                    "page": table.page,
                    "rows": table.shape[0],
                    "columns": table.shape[1],
                    "accuracy": table.accuracy,
                    "html": table_html,
                    "extraction_method": "camelot-lattice"
                }

                # Add URL for accessing the table through the Flask app
                # The URL format needs to be updated to match the new directory structure
                if category:
                    table_info["url"] = f"/{category}/{pdf_base_name}/pdf_tables/{table_filename}"
                else:
                    table_info["url"] = f"/temp_tables/{table_filename}"

                tables.append(table_info)

        # Process stream tables
        for i, table in enumerate(stream_tables):
            if table.df.empty:
                continue

            table_count += 1
            # Convert table to HTML with better styling
            table_html = table.df.to_html(classes='table table-sm table-bordered table-responsive')

            # Enhance the HTML with better styling
            table_html = table_html.replace('<table', '<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;"')
            table_html = table_html.replace('<th', '<th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"')
            table_html = table_html.replace('<td', '<td style="border: 1px solid #dee2e6; padding: 0.3rem;"')

            if save_tables:
                table_filename = f"{pdf_base_name}_stream_{i+1}.html"
                table_path = os.path.join(table_dir, table_filename)

                with open(table_path, "w", encoding="utf-8") as f:
                    f.write(table_html)

                table_info = {
                    "index": table_count,
                    "page": table.page,
                    "rows": table.shape[0],
                    "columns": table.shape[1],
                    "accuracy": table.accuracy,
                    "html": table_html,
                    "extraction_method": "camelot-stream"
                }

                # Add URL for accessing the table through the Flask app
                # The URL format needs to be updated to match the new directory structure
                if category:
                    table_info["url"] = f"/{category}/{pdf_base_name}/pdf_tables/{table_filename}"
                else:
                    table_info["url"] = f"/temp_tables/{table_filename}"

                tables.append(table_info)

        return tables
    except Exception as e:
        logger.error(f"Failed to extract tables with camelot from PDF {pdf_path}: {str(e)}")
        return []

def extract_publication_date_from_text(text):
    """
    Extract publication year and month/month-range from text using regex.
    Returns a dict with keys: year, month_start, month_end, month_range_str.
    Handles both full and abbreviated month names (e.g., Jan, Sept).
    """
    # Patterns for year and month
    year_pattern = r"(19|20)\d{2}"
    # Month names and abbreviations
    months = r"January|February|March|April|May|June|July|August|September|October|November|December|Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Sept|Oct|Nov|Dec"
    # Month range: e.g. January - April 2001 or Jan - Jun 2025
    month_range_pattern = rf"((?:{months})(?:\\s*-\\s*(?:{months}))?)\\s+({year_pattern})"
    # Single month: e.g. March 2020 or Feb 2021
    month_single_pattern = rf"({months})\\s+({year_pattern})"
    # Just year
    just_year_pattern = year_pattern

    # Expanded month mapping (full and abbreviations)
    month_map = {
        "january": 1, "jan": 1,
        "february": 2, "feb": 2,
        "march": 3, "mar": 3,
        "april": 4, "apr": 4,
        "may": 5,
        "june": 6, "jun": 6,
        "july": 7, "jul": 7,
        "august": 8, "aug": 8,
        "september": 9, "sep": 9, "sept": 9,
        "october": 10, "oct": 10,
        "november": 11, "nov": 11,
        "december": 12, "dec": 12
    }

    # Try month range first
    match = re.search(month_range_pattern, text, re.IGNORECASE)
    if match:
        month_range_str = match.group(1).strip()
        year = int(match.group(2))
        # Try to parse start/end months
        months_list = [m.strip() for m in re.split(r"-", month_range_str)]
        month_start = month_end = None
        def normalize_month(m):
            m = m.lower().strip()
            # Use first 4 letters for 'sept', else 3 for others
            if m.startswith('sept'):
                return 'sept'
            return m[:3] if len(m) > 3 else m
        if len(months_list) == 2:
            month_start = month_map.get(normalize_month(months_list[0]), None)
            month_end = month_map.get(normalize_month(months_list[1]), None)
        elif len(months_list) == 1:
            month_start = month_end = month_map.get(normalize_month(months_list[0]), None)
        return {
            "published_year": year,
            "published_month_start": month_start,
            "published_month_end": month_end,
            "published_month_range_str": month_range_str
        }
    # Try single month
    match = re.search(month_single_pattern, text, re.IGNORECASE)
    if match:
        month_str = match.group(1).strip()
        year = int(match.group(2))
        def normalize_month(m):
            m = m.lower().strip()
            if m.startswith('sept'):
                return 'sept'
            return m[:3] if len(m) > 3 else m
        month = month_map.get(normalize_month(month_str), None)
        return {
            "published_year": year,
            "published_month_start": month,
            "published_month_end": month,
            "published_month_range_str": month_str
        }
    # Try just year
    match = re.search(just_year_pattern, text)
    if match:
        year = int(match.group(0))
        return {
            "published_year": year,
            "published_month_start": None,
            "published_month_end": None,
            "published_month_range_str": None
        }
    return {
        "published_year": None,
        "published_month_start": None,
        "published_month_end": None,
        "published_month_range_str": None
    }

@monitor_pdf_processing
def process_pdf(pdf_path, category=None, source_url=None, extract_tables=True, save_images=True, save_tables=True,
              use_vision=None, filter_sensitivity=None, max_images=None, extract_locations=True):
    """
    Process a PDF file to extract text, images, tables, links, and geographical locations.

    This function is the central point for PDF processing and should be called only once per PDF.
    It extracts all content and saves resources in the hierarchical directory structure.

    Args:
        pdf_path: Path to the PDF file
        category: Category for organizing extracted content
        source_url: Original URL where the PDF was obtained
        extract_tables: Whether to extract tables
        save_images: Whether to save extracted images to disk (set to False to only extract metadata)
        save_tables: Whether to save extracted tables to disk
        use_vision: Whether to use vision model for image analysis
        filter_sensitivity: Sensitivity level for filtering images (low, medium, high)
        max_images: Maximum number of images to save

    Returns:
        Dictionary containing extracted content and metadata
    """
    pdf_name = os.path.basename(pdf_path)

    # Always create the directory structure if category is provided
    # This ensures all resources are stored in the hierarchical structure
    if category:
        dir_structure = create_pdf_directory_structure(category, pdf_name)
        if dir_structure:
            # If we're moving an existing PDF to the new structure, copy it to the new location
            if os.path.exists(pdf_path) and not pdf_path.startswith(dir_structure["pdf_dir"]):
                new_pdf_path = dir_structure["pdf_path"]
                try:
                    shutil.copy2(pdf_path, new_pdf_path)
                    logger.info(f"Copied PDF from {pdf_path} to {new_pdf_path}")
                    # Update the pdf_path to the new location
                    pdf_path = new_pdf_path
                except Exception as e:
                    logger.error(f"Failed to copy PDF to new location: {str(e)}")
        else:
            logger.error(f"Failed to create directory structure for {pdf_name}")
    else:
        logger.warning(f"No category provided for PDF {pdf_name}. Resources will be stored in temporary directories.")

    result = {
        "text": [],
        "images": [],
        "tables": [],
        "links": [],
        "metadata": {
            "filename": pdf_name,
            "category": category,
            "source_url": source_url,
            "extraction_date": datetime.now().isoformat()
        }
    }

    # Extract cover image for thumbnail (retain this)
    if category and save_images:
        thumbnail_info = extract_cover_image_from_pdf(pdf_path, category)
        if thumbnail_info:
            result["metadata"]["thumbnail_path"] = thumbnail_info["path"]
            result["metadata"]["thumbnail_url"] = thumbnail_info["url"]
            result["metadata"]["thumbnail_source"] = thumbnail_info["source"]
            result["metadata"]["thumbnail_description"] = thumbnail_info["description"]
            logger.info(f"Added thumbnail from PDF first page for {pdf_name}")
        else:
            # If we couldn't extract a cover image but have a source URL, try to fetch an image now
            if source_url:
                try:
                    import requests
                    from bs4 import BeautifulSoup
                    from urllib.parse import urljoin
                    response = requests.get(source_url, timeout=10)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        images = []
                        for img in soup.find_all('img', src=True):
                            img_url = img['src']
                            if not img_url.startswith(('http://', 'https://')):
                                img_url = urljoin(source_url, img_url)
                            if 'logo' not in img_url.lower() and not img_url.endswith(('.ico', '.svg')):
                                images.append(img_url)
                        if images:
                            cover_image_dir = os.path.join(dir_structure["pdf_images_dir"], "cover_image")
                            os.makedirs(cover_image_dir, exist_ok=True)
                            thumbnail_filename = f"{os.path.splitext(pdf_name)[0]}_url_thumbnail.jpg"
                            thumbnail_path = os.path.join(cover_image_dir, thumbnail_filename)
                            img_response = requests.get(images[0], timeout=10)
                            if img_response.status_code == 200:
                                with open(thumbnail_path, 'wb') as f:
                                    f.write(img_response.content)
                                result["metadata"]["thumbnail_path"] = thumbnail_path
                                result["metadata"]["thumbnail_url"] = f"/{category}/{os.path.splitext(pdf_name)[0]}/pdf_images/cover_image/{thumbnail_filename}"
                                result["metadata"]["thumbnail_source"] = "source_url"
                                result["metadata"]["thumbnail_description"] = f"Image from source URL for {pdf_name}"
                                logger.info(f"Successfully fetched thumbnail from source URL for {pdf_name}")
                            else:
                                result["metadata"]["thumbnail_source"] = "default"
                                logger.warning(f"Failed to download image from {images[0]} for {pdf_name}")
                        else:
                            result["metadata"]["thumbnail_source"] = "default"
                            logger.warning(f"No suitable images found at source URL for {pdf_name}")
                    else:
                        result["metadata"]["thumbnail_source"] = "default"
                        logger.warning(f"Failed to fetch source URL for {pdf_name}: {response.status_code}")
                except Exception as e:
                    result["metadata"]["thumbnail_source"] = "default"
                    logger.error(f"Error fetching thumbnail from source URL for {pdf_name}: {str(e)}")
            else:
                result["metadata"]["thumbnail_source"] = "default"
                logger.info(f"Using default category thumbnail for {pdf_name}")
    elif not category:
        logger.warning(f"No category provided for PDF {pdf_name}, skipping thumbnail extraction")

    try:
        if not os.path.exists(pdf_path):
            logger.error(f"PDF file not found: {pdf_path}")
            return result

        # Extract text using standard method (retain this)
        logger.info(f"Starting text extraction for {pdf_name}")
        standard_text = extract_text_standard(pdf_path)
        result["text"].extend(standard_text)
        logger.info(f"Standard text extraction completed. Found {len(standard_text)} pages with text")

        # If standard extraction found little text, try OCR if available (retain this)
        total_text = sum(len(page["text"]) for page in standard_text)
        logger.info(f"Total text characters extracted: {total_text}")
        if total_text < 1000 and HAS_OCR and HAS_OPENCV:
            logger.info(f"Standard text extraction found limited text ({total_text} chars). Trying OCR...")
            ocr_text = extract_text_with_ocr(pdf_path)
            result["text"].extend(ocr_text)
            logger.info(f"OCR text extraction completed. Found {len(ocr_text)} additional pages with text")
        elif total_text < 1000:
            logger.warning("Limited text found but OCR dependencies not available. Text extraction may be incomplete.")
        logger.info(f"Final text extraction results: {len(result['text'])} pages with text, {total_text} total characters")

        # After extracting text, try to extract publication date from first page (retain this)
        if result["text"]:
            first_page_text = result["text"][0]["text"]
            pubdate = extract_publication_date_from_text(first_page_text)
            result["metadata"].update(pubdate)

        # TODO: Table extraction temporarily disabled
        # # Extract tables if requested and dependencies are available
        # if extract_tables:
        #     tables = []
        #     if HAS_CAMELOT:
        #         tables = extract_tables_with_camelot(pdf_path, category, save_tables)
        #     if not tables and HAS_TABULA:
        #         tables = extract_tables_with_tabula(pdf_path, category, save_tables)
        #     if not tables and extract_tables:
        #         logger.warning("No tables found or table extraction libraries not available.")
        #     result["tables"] = tables

        # TODO: Image extraction (except cover) temporarily disabled
        # images = extract_images_from_pdf(pdf_path, category, save_images, use_vision, filter_sensitivity, max_images)
        # result["images"] = images

        # TODO: Location extraction temporarily disabled
        # if extract_locations:
        #     try:
        #         from app.services.location_extractor import LocationExtractor
        #         from app.utils.database import save_extracted_location, save_location_source
        #         from app.utils.content_db import get_pdf_document_id
        #         logger.info(f"Extracting geographical locations from PDF {pdf_path}")
        #         location_extractor = LocationExtractor()
        #         for page_data in result["text"]:
        #             page_text = page_data.get("text", "")
        #             page_number = page_data.get("page", 1)
        #             if page_text.strip():
        #                 locations = location_extractor.extract_locations_from_text(page_text)
        #                 # ... (rest of location saving logic)
        #         logger.info(f"Extracted and saved {{locations_extracted}} geographical locations from PDF")
        #     except Exception as e:
        #         logger.error(f"Error extracting locations from PDF {pdf_path}: {str(e)}")
        # result["metadata"]["locations_extracted"] = 0

        # Extract links (retain this)
        links = extract_links_from_pdf(pdf_path)
        result["links"] = links

        # Add metadata about extraction results (retain this)
        result["metadata"]["page_count"] = len(result["text"])
        result["metadata"]["image_count"] = 0  # Only cover image is extracted
        result["metadata"]["table_count"] = 0
        result["metadata"]["link_count"] = len(result["links"])
        if category:
            pdf_base_name = os.path.splitext(pdf_name)[0]
            result["metadata"]["pdf_dir"] = f"/{category}/{pdf_base_name}"
        result["metadata"]["locations_extracted"] = 0
        logger.info(f"Successfully processed PDF {pdf_path}: {result['metadata']['page_count']} pages, 0 images, 0 tables, {result['metadata']['link_count']} links, 0 locations (tables/images/locations extraction temporarily disabled)")
        return result
    except Exception as e:
        logger.error(f"Failed to process PDF {pdf_path}: {str(e)}")
        result["metadata"]["error"] = str(e)
        return result

def pdf_to_documents(pdf_path, category=None, source_url=None, use_vision=None, filter_sensitivity=None, max_images=None):
    """
    Convert a processed PDF to LangChain Document objects for vector storage.

    Args:
        pdf_path: Path to the PDF file
        category: Category for organizing content
        source_url: Original URL where the PDF was obtained
        use_vision: Whether to use vision model for image analysis
        filter_sensitivity: Sensitivity level for filtering images (low, medium, high)
        max_images: Maximum number of images to save

    Returns:
        List of Document objects ready for vector storage
    """
    # Process the PDF with vision model analysis if enabled
    processed_pdf = process_pdf(pdf_path, category, source_url,
                               use_vision=use_vision,
                               filter_sensitivity=filter_sensitivity,
                               max_images=max_images)

    if not processed_pdf["text"]:
        logger.warning(f"No text extracted from PDF {pdf_path}")
        return []

    # Create documents
    documents = []
    pdf_filename = os.path.basename(pdf_path)

    for page in processed_pdf["text"]:
        page_num = page["page"]
        page_text = page["text"]

        # Create metadata
        metadata = {
            "source": pdf_filename,
            "original_filename": pdf_filename,
            "citation_filename": os.path.basename(pdf_filename).split("_", 1)[1] if "_" in os.path.basename(pdf_filename) else pdf_filename,  # Remove timestamp prefix for citations
            "page": page_num,
            "type": "pdf",
            "extraction_method": page.get("extraction_method", "standard")
        }

        # Add source URL if provided
        if source_url:
            metadata["original_url"] = source_url

        # Add category if provided
        if category:
            metadata["category"] = category

        # Add images for this page
        page_images = [img for img in processed_pdf["images"] if img.get("page") == page_num]
        if page_images:
            metadata["images"] = json.dumps(page_images)
            metadata["image_count"] = len(page_images)

        # Add tables for this page
        page_tables = [table for table in processed_pdf["tables"] if table.get("page") == page_num]
        if page_tables:
            metadata["tables"] = json.dumps(page_tables)
            metadata["table_count"] = len(page_tables)

        # Add links
        if processed_pdf["links"]:
            metadata["pdf_links"] = json.dumps(processed_pdf["links"])
            metadata["link_count"] = len(processed_pdf["links"])

        # Create document
        documents.append(Document(page_content=page_text, metadata=metadata))

    # Split into chunks for better retrieval
    splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
    chunks = splitter.split_documents(documents)

    # Ensure all chunks have the source filename and URL if provided
    for doc in chunks:
        doc.metadata["source"] = pdf_filename
        doc.metadata["original_filename"] = pdf_filename
        doc.metadata["citation_filename"] = os.path.basename(pdf_filename).split("_", 1)[1] if "_" in os.path.basename(pdf_filename) else pdf_filename  # Remove timestamp prefix for citations
        doc.metadata["type"] = "pdf"
        if source_url:
            doc.metadata["original_url"] = source_url
        if category:
            doc.metadata["category"] = category

        # Robustly add cover image metadata if available
        thumb_meta = processed_pdf.get("metadata", {})
        doc.metadata["thumbnail_path"] = thumb_meta.get("thumbnail_path", "")
        doc.metadata["thumbnail_url"] = thumb_meta.get("thumbnail_url", "")
        doc.metadata["thumbnail_source"] = thumb_meta.get("thumbnail_source", "")
        doc.metadata["thumbnail_description"] = thumb_meta.get("thumbnail_description", "")

    # Warn if no cover image was found
    if not processed_pdf.get("metadata", {}).get("thumbnail_url"):
        logger.warning(f"No cover image (thumbnail_url) found for PDF {pdf_path} after processing. Check extraction and ingestion pipeline.")

    logger.info(f"Created {len(chunks)} document chunks from PDF {pdf_path}")
    return chunks

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
def batch_process_pdfs(pdf_paths: List[str], category: str = None, max_workers: int = None,
                      extract_tables: bool = False, save_images: bool = False,
                      use_vision: bool = None, **kwargs) -> Dict[str, Any]:
    """
    Process multiple PDF files in batch with optimization.

    Args:
        pdf_paths: List of PDF file paths to process
        category: Category for organizing content
        max_workers: Maximum number of worker threads
        extract_tables: Whether to extract tables
        save_images: Whether to save extracted images
        use_vision: Whether to use vision model
        **kwargs: Additional processing parameters

    Returns:
        Dictionary with batch processing results
    """
    if not pdf_paths:
        return {"success": False, "error": "No PDF paths provided"}

    logger.info(f"Starting batch processing of {len(pdf_paths)} PDFs")

    # Prepare processing parameters
    processing_kwargs = {
        'category': category,
        'extract_tables': extract_tables,
        'save_images': save_images,
        'use_vision': use_vision,
        **kwargs
    }

    # Use batch processor
    result = batch_process_documents(
        documents=pdf_paths,
        processor_func=_process_single_pdf_optimized,
        max_workers=max_workers,
        **processing_kwargs
    )

    # Aggregate results
    successful_pdfs = []
    failed_pdfs = []
    total_pages = 0
    total_images = 0
    total_tables = 0

    for i, pdf_result in enumerate(result.results):
        if isinstance(pdf_result, dict) and not pdf_result.get('error'):
            successful_pdfs.append({
                'pdf_path': pdf_paths[i] if i < len(pdf_paths) else 'unknown',
                'result': pdf_result
            })

            # Aggregate statistics
            metadata = pdf_result.get('metadata', {})
            total_pages += metadata.get('page_count', 0)
            total_images += metadata.get('image_count', 0)
            total_tables += metadata.get('table_count', 0)
        else:
            failed_pdfs.append({
                'pdf_path': pdf_paths[i] if i < len(pdf_paths) else 'unknown',
                'error': pdf_result.get('error', 'Unknown error') if isinstance(pdf_result, dict) else str(pdf_result)
            })

    return {
        'success': result.success,
        'processing_time': result.processing_time,
        'total_pdfs': len(pdf_paths),
        'successful_pdfs': len(successful_pdfs),
        'failed_pdfs': len(failed_pdfs),
        'total_pages': total_pages,
        'total_images': total_images,
        'total_tables': total_tables,
        'results': successful_pdfs,
        'errors': failed_pdfs,
        'batch_errors': result.errors
    }

def _process_single_pdf_optimized(pdf_path: str, **kwargs) -> Dict[str, Any]:
    """
    Process a single PDF with optimization and error handling.

    Args:
        pdf_path: Path to the PDF file
        **kwargs: Processing parameters

    Returns:
        Dictionary with processing results
    """
    try:
        # Validate PDF file
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")

        # Check file size
        file_size = os.path.getsize(pdf_path)
        max_size = int(os.getenv('MAX_PDF_SIZE_MB', '100')) * 1024 * 1024  # 100MB default

        if file_size > max_size:
            raise ValueError(f"PDF file too large: {file_size / 1024 / 1024:.1f}MB > {max_size / 1024 / 1024}MB")

        # Process the PDF
        result = process_pdf(pdf_path, **kwargs)

        # Add optimization metadata
        result['metadata']['optimized_processing'] = True
        result['metadata']['file_size_mb'] = file_size / 1024 / 1024

        return result

    except Exception as e:
        logger.error(f"Error processing PDF {pdf_path}: {str(e)}")
        return {
            'error': str(e),
            'pdf_path': pdf_path,
            'metadata': {
                'optimized_processing': False,
                'error_type': type(e).__name__
            }
        }

@performance_monitor(track_memory=True, track_cpu=True)
def optimize_pdf_processing_cache():
    """
    Optimize PDF processing cache and temporary files.
    """
    try:
        temp_folder = os.getenv("TEMP_FOLDER", "./data/temp")
        if not os.path.exists(temp_folder):
            return

        # Clean up old temporary files
        cutoff_time = time.time() - (24 * 60 * 60)  # 24 hours
        removed_count = 0
        freed_space = 0

        for root, dirs, files in os.walk(temp_folder):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    stat = os.stat(file_path)
                    if stat.st_mtime < cutoff_time:
                        file_size = stat.st_size
                        os.remove(file_path)
                        removed_count += 1
                        freed_space += file_size
                except Exception as e:
                    logger.error(f"Error removing temp file {file_path}: {str(e)}")

        if removed_count > 0:
            logger.info(f"PDF cache cleanup: removed {removed_count} files, "
                       f"freed {freed_space / 1024 / 1024:.1f}MB")

        # Clean up empty directories
        for root, dirs, files in os.walk(temp_folder, topdown=False):
            for dir_name in dirs:
                dir_path = os.path.join(root, dir_name)
                try:
                    if not os.listdir(dir_path):  # Empty directory
                        os.rmdir(dir_path)
                except Exception as e:
                    logger.error(f"Error removing empty directory {dir_path}: {str(e)}")

    except Exception as e:
        logger.error(f"Error optimizing PDF processing cache: {str(e)}")

# Schedule PDF cache optimization
def _schedule_pdf_cache_optimization():
    """Schedule periodic PDF cache optimization."""
    import threading

    def cache_cleanup_loop():
        while True:
            try:
                time.sleep(7200)  # Run every 2 hours
                optimize_pdf_processing_cache()
            except Exception as e:
                logger.error(f"Error in PDF cache cleanup loop: {str(e)}")
                time.sleep(600)  # Wait 10 minutes before retrying

    cleanup_thread = threading.Thread(target=cache_cleanup_loop, daemon=True)
    cleanup_thread.start()

# Start PDF cache optimization on module import
_schedule_pdf_cache_optimization()
