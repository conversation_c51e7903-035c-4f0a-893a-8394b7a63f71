"""
Performance Monitoring System for ERDB PDF Processing

This module provides comprehensive performance monitoring including:
- Function timing decorators with memory tracking
- Bottleneck detection and analysis
- Database operation performance monitoring
- Vision model processing metrics
- ChromaDB vector operation timing
- Performance dashboard data collection
"""

import time
import psutil
import logging
import functools
import threading
import sqlite3
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import traceback

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """Individual performance metric data."""
    function_name: str
    module_name: str
    execution_time: float
    memory_before_mb: float
    memory_after_mb: float
    memory_peak_mb: float
    cpu_percent: float
    timestamp: str
    parameters: Dict[str, Any]
    result_size: Optional[int] = None
    error: Optional[str] = None

@dataclass
class DatabaseMetric:
    """Database operation performance metric."""
    operation_type: str  # 'SELECT', 'INSERT', 'UPDATE', 'DELETE'
    table_name: str
    execution_time: float
    rows_affected: int
    query_hash: str
    timestamp: str
    database_name: str
    error: Optional[str] = None

@dataclass
class BottleneckAlert:
    """Bottleneck detection alert."""
    function_name: str
    severity: str  # 'warning', 'critical'
    message: str
    execution_time: float
    memory_usage_mb: float
    timestamp: str
    recommendations: List[str]

class PerformanceMonitor:
    """Central performance monitoring system."""
    
    def __init__(self, max_metrics: int = 10000):
        self.metrics: deque = deque(maxlen=max_metrics)
        self.db_metrics: deque = deque(maxlen=max_metrics)
        self.bottlenecks: deque = deque(maxlen=1000)
        self.function_stats: Dict[str, Dict] = defaultdict(lambda: {
            'total_calls': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'max_time': 0.0,
            'min_time': float('inf'),
            'total_memory': 0.0,
            'avg_memory': 0.0,
            'error_count': 0
        })
        self.lock = threading.Lock()
        
        # Performance thresholds
        self.thresholds = {
            'execution_time_warning': 5.0,  # seconds
            'execution_time_critical': 15.0,  # seconds
            'memory_usage_warning': 500.0,  # MB
            'memory_usage_critical': 1000.0,  # MB
            'cpu_usage_warning': 80.0,  # percent
            'cpu_usage_critical': 95.0  # percent
        }
    
    def add_metric(self, metric: PerformanceMetric):
        """Add a performance metric to the collection."""
        with self.lock:
            self.metrics.append(metric)
            self._update_function_stats(metric)
            self._check_bottlenecks(metric)
    
    def add_db_metric(self, metric: DatabaseMetric):
        """Add a database performance metric."""
        with self.lock:
            self.db_metrics.append(metric)
    
    def _update_function_stats(self, metric: PerformanceMetric):
        """Update aggregated function statistics."""
        key = f"{metric.module_name}.{metric.function_name}"
        stats = self.function_stats[key]
        
        stats['total_calls'] += 1
        stats['total_time'] += metric.execution_time
        stats['avg_time'] = stats['total_time'] / stats['total_calls']
        stats['max_time'] = max(stats['max_time'], metric.execution_time)
        stats['min_time'] = min(stats['min_time'], metric.execution_time)
        
        memory_used = metric.memory_after_mb - metric.memory_before_mb
        stats['total_memory'] += memory_used
        stats['avg_memory'] = stats['total_memory'] / stats['total_calls']
        
        if metric.error:
            stats['error_count'] += 1
    
    def _check_bottlenecks(self, metric: PerformanceMetric):
        """Check for performance bottlenecks and create alerts."""
        alerts = []
        
        # Check execution time
        if metric.execution_time > self.thresholds['execution_time_critical']:
            alerts.append(BottleneckAlert(
                function_name=f"{metric.module_name}.{metric.function_name}",
                severity='critical',
                message=f"Critical execution time: {metric.execution_time:.2f}s",
                execution_time=metric.execution_time,
                memory_usage_mb=metric.memory_peak_mb,
                timestamp=metric.timestamp,
                recommendations=[
                    "Consider optimizing algorithm complexity",
                    "Check for inefficient database queries",
                    "Review memory allocation patterns",
                    "Consider implementing caching"
                ]
            ))
        elif metric.execution_time > self.thresholds['execution_time_warning']:
            alerts.append(BottleneckAlert(
                function_name=f"{metric.module_name}.{metric.function_name}",
                severity='warning',
                message=f"High execution time: {metric.execution_time:.2f}s",
                execution_time=metric.execution_time,
                memory_usage_mb=metric.memory_peak_mb,
                timestamp=metric.timestamp,
                recommendations=[
                    "Monitor function performance",
                    "Consider performance profiling"
                ]
            ))
        
        # Check memory usage
        memory_used = metric.memory_peak_mb - metric.memory_before_mb
        if memory_used > self.thresholds['memory_usage_critical']:
            alerts.append(BottleneckAlert(
                function_name=f"{metric.module_name}.{metric.function_name}",
                severity='critical',
                message=f"Critical memory usage: {memory_used:.2f}MB",
                execution_time=metric.execution_time,
                memory_usage_mb=memory_used,
                timestamp=metric.timestamp,
                recommendations=[
                    "Review memory allocation and cleanup",
                    "Consider processing data in smaller chunks",
                    "Implement memory-efficient algorithms",
                    "Check for memory leaks"
                ]
            ))
        elif memory_used > self.thresholds['memory_usage_warning']:
            alerts.append(BottleneckAlert(
                function_name=f"{metric.module_name}.{metric.function_name}",
                severity='warning',
                message=f"High memory usage: {memory_used:.2f}MB",
                execution_time=metric.execution_time,
                memory_usage_mb=memory_used,
                timestamp=metric.timestamp,
                recommendations=[
                    "Monitor memory usage patterns",
                    "Consider memory optimization"
                ]
            ))
        
        # Add alerts to collection
        for alert in alerts:
            self.bottlenecks.append(alert)
            logger.warning(f"Performance bottleneck detected: {alert.message}")
    
    def get_function_stats(self, function_name: Optional[str] = None) -> Dict[str, Any]:
        """Get aggregated function performance statistics."""
        with self.lock:
            if function_name:
                return dict(self.function_stats.get(function_name, {}))
            return {k: dict(v) for k, v in self.function_stats.items()}
    
    def get_recent_metrics(self, hours: int = 1) -> List[PerformanceMetric]:
        """Get performance metrics from the last N hours."""
        cutoff = datetime.now() - timedelta(hours=hours)
        cutoff_str = cutoff.isoformat()
        
        with self.lock:
            return [m for m in self.metrics if m.timestamp >= cutoff_str]
    
    def get_bottlenecks(self, severity: Optional[str] = None) -> List[BottleneckAlert]:
        """Get recent bottleneck alerts."""
        with self.lock:
            if severity:
                return [b for b in self.bottlenecks if b.severity == severity]
            return list(self.bottlenecks)
    
    def export_metrics(self, filepath: str) -> bool:
        """Export all metrics to JSON file."""
        try:
            with self.lock:
                data = {
                    'performance_metrics': [asdict(m) for m in self.metrics],
                    'database_metrics': [asdict(m) for m in self.db_metrics],
                    'bottleneck_alerts': [asdict(b) for b in self.bottlenecks],
                    'function_statistics': dict(self.function_stats),
                    'export_timestamp': datetime.now().isoformat()
                }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Performance metrics exported to {filepath}")
            return True
        except Exception as e:
            logger.error(f"Failed to export metrics: {str(e)}")
            return False

# Global performance monitor instance
_performance_monitor: Optional[PerformanceMonitor] = None

def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor

def performance_monitor(
    track_memory: bool = True,
    track_cpu: bool = True,
    log_parameters: bool = False,
    log_result_size: bool = False
):
    """
    Decorator for monitoring function performance.
    
    Args:
        track_memory: Whether to track memory usage
        track_cpu: Whether to track CPU usage
        log_parameters: Whether to log function parameters
        log_result_size: Whether to log result size
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            monitor = get_performance_monitor()
            
            # Get initial metrics
            start_time = time.time()
            process = psutil.Process() if track_memory or track_cpu else None
            
            memory_before = process.memory_info().rss / 1024 / 1024 if track_memory else 0
            cpu_before = process.cpu_percent() if track_cpu else 0
            
            # Track peak memory during execution
            peak_memory = memory_before
            
            try:
                # Execute function
                result = func(*args, **kwargs)
                
                # Get final metrics
                end_time = time.time()
                execution_time = end_time - start_time
                
                memory_after = process.memory_info().rss / 1024 / 1024 if track_memory else 0
                cpu_after = process.cpu_percent() if track_cpu else 0
                
                # Estimate peak memory (simplified)
                peak_memory = max(memory_before, memory_after)
                
                # Prepare parameters for logging
                parameters = {}
                if log_parameters:
                    try:
                        # Log basic parameter info (avoid large objects)
                        parameters = {
                            'args_count': len(args),
                            'kwargs_keys': list(kwargs.keys()),
                            'args_types': [type(arg).__name__ for arg in args[:5]]  # First 5 only
                        }
                    except:
                        parameters = {'error': 'Failed to serialize parameters'}
                
                # Get result size
                result_size = None
                if log_result_size and result is not None:
                    try:
                        if hasattr(result, '__len__'):
                            result_size = len(result)
                        elif hasattr(result, 'size'):
                            result_size = result.size
                    except:
                        pass
                
                # Create performance metric
                metric = PerformanceMetric(
                    function_name=func.__name__,
                    module_name=func.__module__,
                    execution_time=execution_time,
                    memory_before_mb=memory_before,
                    memory_after_mb=memory_after,
                    memory_peak_mb=peak_memory,
                    cpu_percent=(cpu_before + cpu_after) / 2,
                    timestamp=datetime.now().isoformat(),
                    parameters=parameters,
                    result_size=result_size
                )
                
                monitor.add_metric(metric)
                
                # Log performance info
                logger.info(f"Performance: {func.__module__}.{func.__name__} "
                           f"executed in {execution_time:.3f}s, "
                           f"memory: {memory_before:.1f}→{memory_after:.1f}MB")
                
                return result
                
            except Exception as e:
                # Record error metric
                end_time = time.time()
                execution_time = end_time - start_time
                
                memory_after = process.memory_info().rss / 1024 / 1024 if track_memory else 0
                
                metric = PerformanceMetric(
                    function_name=func.__name__,
                    module_name=func.__module__,
                    execution_time=execution_time,
                    memory_before_mb=memory_before,
                    memory_after_mb=memory_after,
                    memory_peak_mb=peak_memory,
                    cpu_percent=cpu_before,
                    timestamp=datetime.now().isoformat(),
                    parameters={},
                    error=str(e)
                )
                
                monitor.add_metric(metric)
                
                logger.error(f"Performance: {func.__module__}.{func.__name__} "
                           f"failed after {execution_time:.3f}s: {str(e)}")
                
                raise
        
        return wrapper
    return decorator

def database_monitor(operation_type: str = None, table_name: str = None):
    """
    Decorator for monitoring database operations.

    Args:
        operation_type: Type of database operation (SELECT, INSERT, UPDATE, DELETE)
        table_name: Name of the table being operated on
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            monitor = get_performance_monitor()

            start_time = time.time()
            rows_affected = 0
            error = None

            try:
                result = func(*args, **kwargs)

                # Try to determine rows affected
                if hasattr(result, '__len__'):
                    rows_affected = len(result)
                elif isinstance(result, int):
                    rows_affected = result

                return result

            except Exception as e:
                error = str(e)
                raise

            finally:
                end_time = time.time()
                execution_time = end_time - start_time

                # Try to extract operation details from function name or args
                op_type = operation_type or _extract_operation_type(func.__name__, args, kwargs)
                table = table_name or _extract_table_name(func.__name__, args, kwargs)

                # Create query hash for tracking
                query_hash = _create_query_hash(func.__name__, args, kwargs)

                metric = DatabaseMetric(
                    operation_type=op_type,
                    table_name=table,
                    execution_time=execution_time,
                    rows_affected=rows_affected,
                    query_hash=query_hash,
                    timestamp=datetime.now().isoformat(),
                    database_name=_extract_database_name(args, kwargs),
                    error=error
                )

                monitor.add_db_metric(metric)

                if execution_time > 1.0:  # Log slow queries
                    logger.warning(f"Slow database operation: {op_type} on {table} "
                                 f"took {execution_time:.3f}s")

        return wrapper
    return decorator

def _extract_operation_type(func_name: str, args: tuple, kwargs: dict) -> str:
    """Extract database operation type from function context."""
    func_lower = func_name.lower()

    if 'select' in func_lower or 'query' in func_lower or 'get' in func_lower:
        return 'SELECT'
    elif 'insert' in func_lower or 'create' in func_lower or 'add' in func_lower:
        return 'INSERT'
    elif 'update' in func_lower or 'modify' in func_lower:
        return 'UPDATE'
    elif 'delete' in func_lower or 'remove' in func_lower:
        return 'DELETE'

    # Try to extract from SQL query in arguments
    for arg in args:
        if isinstance(arg, str) and len(arg) > 10:
            arg_upper = arg.upper().strip()
            if arg_upper.startswith('SELECT'):
                return 'SELECT'
            elif arg_upper.startswith('INSERT'):
                return 'INSERT'
            elif arg_upper.startswith('UPDATE'):
                return 'UPDATE'
            elif arg_upper.startswith('DELETE'):
                return 'DELETE'

    return 'UNKNOWN'

def _extract_table_name(func_name: str, args: tuple, kwargs: dict) -> str:
    """Extract table name from function context."""
    # Try to extract from function name
    common_tables = ['users', 'chat_history', 'pdf_documents', 'source_urls',
                    'url_content', 'cover_images', 'extracted_locations']

    func_lower = func_name.lower()
    for table in common_tables:
        if table in func_lower:
            return table

    # Try to extract from SQL query
    for arg in args:
        if isinstance(arg, str) and len(arg) > 10:
            for table in common_tables:
                if table in arg.lower():
                    return table

    return 'unknown'

def _extract_database_name(args: tuple, kwargs: dict) -> str:
    """Extract database name from function arguments."""
    # Look for database path in arguments
    for arg in args:
        if isinstance(arg, str) and '.db' in arg:
            return os.path.basename(arg)

    for value in kwargs.values():
        if isinstance(value, str) and '.db' in value:
            return os.path.basename(value)

    return 'unknown'

def _create_query_hash(func_name: str, args: tuple, kwargs: dict) -> str:
    """Create a hash for tracking similar queries."""
    import hashlib

    # Create a simplified representation for hashing
    hash_input = f"{func_name}_{len(args)}_{sorted(kwargs.keys())}"

    # Add first SQL query if found
    for arg in args:
        if isinstance(arg, str) and len(arg) > 10:
            # Normalize query for hashing (remove specific values)
            normalized = arg.upper().strip()
            # Replace parameter placeholders
            import re
            normalized = re.sub(r'\?', 'PARAM', normalized)
            normalized = re.sub(r'\'[^\']*\'', 'STRING', normalized)
            normalized = re.sub(r'\d+', 'NUMBER', normalized)
            hash_input += f"_{normalized[:100]}"  # First 100 chars
            break

    return hashlib.md5(hash_input.encode()).hexdigest()[:16]

# Convenience functions for common monitoring scenarios
def monitor_pdf_processing(func):
    """Decorator specifically for PDF processing functions."""
    return performance_monitor(
        track_memory=True,
        track_cpu=True,
        log_parameters=True,
        log_result_size=True
    )(func)

def monitor_embedding_operation(func):
    """Decorator specifically for embedding operations."""
    return performance_monitor(
        track_memory=True,
        track_cpu=True,
        log_parameters=False,  # Embeddings can be large
        log_result_size=True
    )(func)

def monitor_vision_processing(func):
    """Decorator specifically for vision model operations."""
    return performance_monitor(
        track_memory=True,
        track_cpu=True,
        log_parameters=True,
        log_result_size=False
    )(func)

def monitor_database_query(func):
    """Decorator specifically for database query operations."""
    return database_monitor()(func)
