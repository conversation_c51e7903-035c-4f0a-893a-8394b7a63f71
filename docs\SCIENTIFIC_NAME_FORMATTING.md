# Scientific Name Formatting Feature

## Overview

The ERDB Knowledge Products system automatically formats scientific names (binomial nomenclature) using a hybrid approach that leverages LLM capabilities and frontend CSS styling. The system handles both traditional italic markdown (`*text*`) and LLM-generated bold markdown (`**text**`) to ensure consistent italicization of scientific names in all responses.

## Features

### LLM-Based Detection
The system leverages the natural language understanding capabilities of the LLM models (llama3.2:3b-instruct-q4_K_M and llama3.1:8b-instruct-q4_K_M) to identify and format scientific names. These models can recognize various scientific name formats:
- **Basic Binomial**: *Homo sapiens*, *Pterocarpus indicus*
- **Trinomial with Subspecies**: *Homo sapiens sapiens*, *Pinus kesiya var. langbianensis*
- **With Author Citations**: *Escherichia coli* (Migula 1895), *Swietenia macrophylla* King
- **Abbreviated Forms**: *E. coli*, *Pinus sp.*

### Frontend Styling
- The LLM outputs scientific names using markdown formatting (`**text**` for emphasis)
- Frontend CSS converts both `<strong>` (bold) and `<em>` (italic) tags to italic appearance
- This ensures all scientific names are consistently italicized regardless of the original markdown format

### Consistency
- The LLM automatically formats scientific names using markdown emphasis
- Frontend CSS ensures consistent italic display for both bold and italic markdown
- Backend ScispaCy processing is available as a fallback for any missed scientific names

## Usage Examples

### LLM Output (Bold Markdown)
```
The study examined **Homo sapiens** and **Escherichia coli** in various environments.
We also looked at **Pterocarpus indicus**, which is native to the Philippines.
The species **Swietenia macrophylla** King is an important timber species.
```

### Frontend Processing
The markdown is converted to HTML by marked.js:
```html
The study examined <strong>Homo sapiens</strong> and <strong>Escherichia coli</strong> in various environments.
We also looked at <strong>Pterocarpus indicus</strong>, which is native to the Philippines.
The species <strong>Swietenia macrophylla</strong> King is an important timber species.
```

### CSS Styling
The CSS converts `<strong>` tags to italic appearance:
```css
strong {
    font-style: italic;
    font-weight: normal;
}
```

### Final Visual Result
The study examined *Homo sapiens* and *Escherichia coli* in various environments.
We also looked at *Pterocarpus indicus*, which is native to the Philippines.
The species *Swietenia macrophylla* King is an important timber species.

## Configuration
- The LLM models automatically format scientific names using markdown emphasis
- Frontend CSS styling is defined in `app/templates/index.html` and `app/static/frontend/Canopy-Rise.html`
- Backend ScispaCy processing is available as a fallback in `app/utils/validate_and_italicize_scientific_names.py`

## Why This Hybrid Approach?
- **LLM Intelligence:** Leverages the natural language understanding of modern LLMs
- **Performance:** Faster response times (no additional NLP processing required)
- **Flexibility:** Handles both bold and italic markdown formats
- **Fallback:** ScispaCy processing available for any missed scientific names
- **Consistency:** CSS ensures uniform italic display regardless of source format

## Testing
- Test queries with scientific names in both main answers and follow-up questions
- Confirm that all scientific names are italicized in the UI
- Test with both llama3.2:3b and llama3.1:8b models
- Verify that both bold (`**text**`) and italic (`*text*`) markdown formats display as italics

## Test File
A test file `test_scientific_names.html` has been created to demonstrate the formatting:
- Shows examples of both bold and italic markdown
- Includes Philippine species examples
- Demonstrates various scientific name formats

## Notes
- The LLM models (llama3.2:3b and llama3.1:8b) automatically format scientific names using markdown emphasis
- Frontend CSS handles the conversion from bold/italic markdown to italic display
- Backend ScispaCy processing remains available as a fallback
- If scientific names are not italicized, check that the CSS is properly loaded and the LLM is formatting correctly
