#!/usr/bin/env python3
"""
Setup Script for ERDB Performance Monitoring System

This script sets up the comprehensive performance monitoring system including:
- Database optimization and indexing
- Performance monitoring configuration
- Alert rules setup
- Dashboard initialization
- Cache optimization
- Logging configuration
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_directories():
    """Create necessary directories for performance monitoring."""
    directories = [
        './logs',
        './reports',
        './exports/performance',
        './config',
        './data/temp'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def setup_environment_variables():
    """Setup environment variables for performance monitoring."""
    env_vars = {
        # Performance monitoring settings
        'BATCH_MAX_WORKERS': str(os.cpu_count()),
        'BATCH_DEFAULT_CHUNK_SIZE': '10',
        'BATCH_CPU_THRESHOLD': '80.0',
        'BATCH_MEMORY_THRESHOLD': '85.0',
        'BATCH_CACHE_ENABLED': 'true',
        
        # Vision model cache settings
        'VISION_CACHE_MAX_SIZE_MB': '500',
        'USE_VISION_MODEL': 'true',
        'USE_VISION_MODEL_DURING_EMBEDDING': 'true',
        
        # PDF processing settings
        'MAX_PDF_SIZE_MB': '100',
        'TEMP_FOLDER': './data/temp',
        
        # Database settings
        'DB_PATH': './erdb_main.db',
        
        # Logging settings
        'LOG_DIR': './logs',
        'FLASK_ENV': 'production',
        
        # Alert settings (optional - configure if email alerts needed)
        # 'SMTP_SERVER': 'smtp.gmail.com',
        # 'SMTP_PORT': '587',
        # 'SMTP_USERNAME': '<EMAIL>',
        # 'SMTP_PASSWORD': 'your-app-password',
        # 'ALERT_RECIPIENTS': '<EMAIL>,<EMAIL>',
        
        # Alert rules file
        'ALERT_RULES_FILE': './config/alert_rules.json'
    }
    
    env_file = '.env'
    existing_vars = {}
    
    # Read existing .env file if it exists
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    existing_vars[key] = value
    
    # Add new variables that don't exist
    new_vars = {}
    for key, value in env_vars.items():
        if key not in existing_vars:
            new_vars[key] = value
    
    if new_vars:
        with open(env_file, 'a') as f:
            f.write(f"\n# Performance monitoring settings added on {datetime.now().isoformat()}\n")
            for key, value in new_vars.items():
                f.write(f"{key}={value}\n")
        
        logger.info(f"Added {len(new_vars)} environment variables to {env_file}")
    else:
        logger.info("All environment variables already configured")

def setup_alert_rules():
    """Setup default alert rules configuration."""
    alert_rules = [
        {
            "name": "slow_function_execution",
            "condition": "execution_time",
            "threshold": 5.0,
            "severity": "warning",
            "cooldown_minutes": 10,
            "enabled": True
        },
        {
            "name": "critical_function_execution",
            "condition": "execution_time",
            "threshold": 15.0,
            "severity": "critical",
            "cooldown_minutes": 5,
            "enabled": True
        },
        {
            "name": "high_memory_usage",
            "condition": "memory_usage",
            "threshold": 500.0,
            "severity": "warning",
            "cooldown_minutes": 15,
            "enabled": True
        },
        {
            "name": "critical_memory_usage",
            "condition": "memory_usage",
            "threshold": 1000.0,
            "severity": "critical",
            "cooldown_minutes": 5,
            "enabled": True
        },
        {
            "name": "high_error_rate",
            "condition": "error_rate",
            "threshold": 0.05,
            "severity": "warning",
            "cooldown_minutes": 10,
            "enabled": True
        },
        {
            "name": "system_health_warning",
            "condition": "system_health",
            "threshold": 70.0,
            "severity": "warning",
            "cooldown_minutes": 30,
            "enabled": True
        },
        {
            "name": "system_health_critical",
            "condition": "system_health",
            "threshold": 50.0,
            "severity": "critical",
            "cooldown_minutes": 15,
            "enabled": True
        },
        {
            "name": "pdf_processing_slow",
            "condition": "execution_time",
            "threshold": 30.0,
            "severity": "warning",
            "function_pattern": "pdf_processor",
            "cooldown_minutes": 20,
            "enabled": True
        },
        {
            "name": "vision_processing_slow",
            "condition": "execution_time",
            "threshold": 10.0,
            "severity": "warning",
            "function_pattern": "vision_processor",
            "cooldown_minutes": 15,
            "enabled": True
        },
        {
            "name": "embedding_processing_slow",
            "condition": "execution_time",
            "threshold": 20.0,
            "severity": "warning",
            "function_pattern": "embedding_service",
            "cooldown_minutes": 15,
            "enabled": True
        }
    ]
    
    rules_file = './config/alert_rules.json'
    
    if not os.path.exists(rules_file):
        with open(rules_file, 'w') as f:
            json.dump(alert_rules, f, indent=2)
        logger.info(f"Created alert rules configuration: {rules_file}")
    else:
        logger.info(f"Alert rules configuration already exists: {rules_file}")

def optimize_databases():
    """Run database optimization."""
    try:
        from app.utils.database_optimizer import get_database_optimizer
        
        # Get database paths
        db_paths = [
            os.getenv('DB_PATH', './erdb_main.db'),
            './user_management.db',
            './chat_history.db',
            './content_db.sqlite'
        ]
        
        for db_path in db_paths:
            if os.path.exists(db_path):
                logger.info(f"Optimizing database: {db_path}")
                
                optimizer = get_database_optimizer(db_path)
                
                # Create recommended indexes
                index_results = optimizer.create_recommended_indexes()
                created_count = len([r for r in index_results if r['status'] == 'created'])
                
                logger.info(f"Created {created_count} indexes for {db_path}")
            else:
                logger.warning(f"Database not found: {db_path}")
    
    except Exception as e:
        logger.error(f"Error optimizing databases: {str(e)}")

def setup_flask_integration():
    """Setup Flask integration for performance monitoring."""
    integration_code = '''
# Add this to your main Flask app file (e.g., app.py or __init__.py)

from app.routes.performance import performance_bp
from app.utils.performance_logger import get_performance_logger
from app.utils.health_monitor import get_health_monitor

# Register performance monitoring blueprint
app.register_blueprint(performance_bp)

# Initialize performance monitoring
performance_logger = get_performance_logger()
health_monitor = get_health_monitor()

# Optional: Add performance monitoring to all requests
@app.before_request
def before_request():
    from flask import g
    import time
    g.start_time = time.time()

@app.after_request
def after_request(response):
    from flask import g, request
    import time
    
    if hasattr(g, 'start_time'):
        execution_time = time.time() - g.start_time
        
        # Log slow requests
        if execution_time > 2.0:  # Log requests taking more than 2 seconds
            logger.warning(f"Slow request: {request.method} {request.path} took {execution_time:.3f}s")
    
    return response
'''
    
    integration_file = './flask_performance_integration.py'
    
    if not os.path.exists(integration_file):
        with open(integration_file, 'w') as f:
            f.write(integration_code)
        logger.info(f"Created Flask integration example: {integration_file}")

def create_monitoring_dashboard_url():
    """Create a simple script to open the monitoring dashboard."""
    script_content = '''#!/usr/bin/env python3
"""
Open ERDB Performance Monitoring Dashboard

This script opens the performance monitoring dashboard in your default browser.
Make sure your Flask application is running before executing this script.
"""

import webbrowser
import os

# Default Flask development server URL
base_url = os.getenv('FLASK_BASE_URL', 'http://localhost:5000')
dashboard_url = f"{base_url}/performance/"

print(f"Opening performance dashboard: {dashboard_url}")
webbrowser.open(dashboard_url)
'''
    
    script_file = './open_dashboard.py'
    
    if not os.path.exists(script_file):
        with open(script_file, 'w') as f:
            f.write(script_content)
        
        # Make script executable on Unix systems
        if os.name != 'nt':
            os.chmod(script_file, 0o755)
        
        logger.info(f"Created dashboard opener script: {script_file}")

def run_initial_optimization():
    """Run initial system optimization."""
    try:
        # Run database optimization script
        logger.info("Running initial database optimization...")
        os.system(f"python {project_root}/scripts/maintenance/optimize_database.py")
        
        # Initialize performance monitoring
        logger.info("Initializing performance monitoring...")
        from app.utils.performance_monitor import get_performance_monitor
        from app.utils.health_monitor import get_health_monitor
        
        perf_monitor = get_performance_monitor()
        health_monitor = get_health_monitor()
        
        logger.info("Performance monitoring initialized successfully")
        
    except Exception as e:
        logger.error(f"Error during initial optimization: {str(e)}")

def print_setup_summary():
    """Print setup summary and next steps."""
    print("\n" + "="*60)
    print("ERDB PERFORMANCE MONITORING SETUP COMPLETE")
    print("="*60)
    print("\nSetup Summary:")
    print("✓ Created necessary directories")
    print("✓ Configured environment variables")
    print("✓ Setup alert rules")
    print("✓ Optimized databases")
    print("✓ Created Flask integration example")
    print("✓ Created dashboard opener script")
    print("✓ Ran initial optimization")
    
    print("\nNext Steps:")
    print("1. Review and update environment variables in .env file")
    print("2. Configure email settings for alerts (optional)")
    print("3. Integrate performance monitoring into your Flask app using flask_performance_integration.py")
    print("4. Start your Flask application")
    print("5. Open the performance dashboard: python open_dashboard.py")
    print("6. Monitor the logs directory for performance logs")
    
    print("\nPerformance Dashboard URL:")
    print("http://localhost:5000/performance/")
    
    print("\nKey Files Created:")
    print("- .env (environment variables)")
    print("- config/alert_rules.json (alert configuration)")
    print("- flask_performance_integration.py (Flask integration)")
    print("- open_dashboard.py (dashboard opener)")
    
    print("\nMonitoring Features:")
    print("- Real-time performance metrics")
    print("- Database optimization")
    print("- Memory and CPU monitoring")
    print("- Bottleneck detection")
    print("- Automated alerting")
    print("- Performance trends analysis")
    print("- Batch processing optimization")
    
    print("\n" + "="*60)

def main():
    """Main setup function."""
    logger.info("Starting ERDB Performance Monitoring setup...")
    
    try:
        setup_directories()
        setup_environment_variables()
        setup_alert_rules()
        optimize_databases()
        setup_flask_integration()
        create_monitoring_dashboard_url()
        run_initial_optimization()
        
        print_setup_summary()
        
        logger.info("Performance monitoring setup completed successfully!")
        return 0
        
    except Exception as e:
        logger.error(f"Setup failed: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
