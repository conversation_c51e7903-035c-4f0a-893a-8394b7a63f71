{% extends "admin_base.html" %}

{% block title %}System Health & Performance - Admin Dashboard{% endblock %}

{% block extra_head %}
<!-- Ensure CSRF token is available for JavaScript -->
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-heartbeat fa-fw me-2"></i>System Health & Performance Monitor
        </h1>
        <div>
            <button onclick="optimizeDatabase()" class="btn btn-primary btn-sm me-2">
                <i class="fas fa-database fa-fw me-1"></i>Optimize DB
            </button>
            <a href="{{ url_for('admin.export_health_metrics') }}" class="btn btn-outline-primary btn-sm me-2">
                <i class="fas fa-download fa-fw me-1"></i>Export Metrics
            </a>
            <button onclick="refreshMetrics()" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-sync-alt fa-fw me-1" id="refresh-icon"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Health Status Overview -->
    <div class="row mb-4">
        <div class="col-xl-12">
            <div class="card border-left-{{ 'success' if health_status.status == 'healthy' else 'warning' if health_status.status == 'warning' else 'danger' }} shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                System Health Status
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="health-status">
                                {{ health_status.status.title() }} 
                                <span class="badge bg-{{ 'success' if health_status.status == 'healthy' else 'warning' if health_status.status == 'warning' else 'danger' }}">
                                    Score: {{ health_status.score }}/100
                                </span>
                            </div>
                            <div class="text-xs text-muted mt-1">
                                Last checked: {{ health_status.last_check }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-heartbeat fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Metrics -->
    <div class="row mb-4" id="system-metrics">
        <!-- CPU Usage -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-{{ 'danger' if system_metrics.cpu_percent > 90 else 'warning' if system_metrics.cpu_percent > 70 else 'success' }} shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">CPU Usage</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ "%.1f"|format(system_metrics.cpu_percent) }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-microchip fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Memory Usage -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-{{ 'danger' if system_metrics.memory_percent > 95 else 'warning' if system_metrics.memory_percent > 80 else 'success' }} shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Memory Usage</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ "%.1f"|format(system_metrics.memory_percent) }}%</div>
                            <div class="text-xs text-muted">{{ "%.1f"|format(system_metrics.memory_available_mb) }} MB available</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-memory fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Disk Usage -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-{{ 'danger' if system_metrics.disk_usage_percent > 95 else 'warning' if system_metrics.disk_usage_percent > 85 else 'success' }} shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Disk Usage</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ "%.1f"|format(system_metrics.disk_usage_percent) }}%</div>
                            <div class="text-xs text-muted">{{ "%.1f"|format(system_metrics.disk_free_gb) }} GB free</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hdd fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Size -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-{{ 'danger' if system_metrics.database_size_mb > 5000 else 'warning' if system_metrics.database_size_mb > 1000 else 'success' }} shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Database Size</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ "%.1f"|format(system_metrics.database_size_mb) }} MB</div>
                            <div class="text-xs text-muted">{{ system_metrics.active_connections }} active connections</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-database fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Alerts -->
    <div class="row mb-4" id="performance-alerts" style="display: none;">
        <div class="col-xl-12">
            <div class="card border-left-warning shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-triangle fa-fw me-2"></i>Performance Alerts
                    </h6>
                </div>
                <div class="card-body" id="alerts-container">
                    <!-- Alerts will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics Row -->
    <div class="row mb-4">
        <!-- Function Performance -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line fa-fw me-2"></i>Function Performance
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm" id="function-performance-table">
                            <thead>
                                <tr>
                                    <th>Function</th>
                                    <th>Calls</th>
                                    <th>Avg Time (s)</th>
                                    <th>Max Time (s)</th>
                                    <th>Avg Memory (MB)</th>
                                    <th>Errors</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Performance data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Batch Processing Status -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tasks fa-fw me-2"></i>Batch Processing Status
                    </h6>
                </div>
                <div class="card-body" id="batch-status">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-2">Loading batch processing status...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Optimization Row -->
    <div class="row mb-4">
        <!-- Database Health -->
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-database fa-fw me-2"></i>Database Health & Optimization
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Database</th>
                                    <th>Size (MB)</th>
                                    <th>Pages</th>
                                    <th>Cache Hit Ratio</th>
                                    <th>Integrity</th>
                                    <th>Last Vacuum</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for db in db_metrics %}
                                <tr>
                                    <td>{{ db.database_name }}</td>
                                    <td>{{ "%.1f"|format(db.size_mb) }}</td>
                                    <td>{{ db.page_count }}</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-{{ 'success' if db.cache_hit_ratio > 0.8 else 'warning' if db.cache_hit_ratio > 0.6 else 'danger' }}" 
                                                 style="width: {{ (db.cache_hit_ratio * 100)|round }}%">
                                                {{ "%.1f"|format(db.cache_hit_ratio * 100) }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if db.integrity_check %}
                                        <span class="badge bg-success">OK</span>
                                        {% else %}
                                        <span class="badge bg-danger">Failed</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ db.last_vacuum or 'Never' }}</td>
                                    <td>
                                        {% if db.size_mb > 5000 %}
                                        <span class="badge bg-danger">Critical</span>
                                        {% elif db.size_mb > 1000 %}
                                        <span class="badge bg-warning">Warning</span>
                                        {% else %}
                                        <span class="badge bg-success">Healthy</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Optimization Panel -->
        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tools fa-fw me-2"></i>Database Optimization
                    </h6>
                </div>
                <div class="card-body" id="optimization-panel">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-2">Loading optimization data...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Issues and Recommendations -->
    {% if health_status.issues %}
    <div class="row mb-4">
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle fa-fw me-2"></i>Issues Detected
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        {% for issue in health_status.issues %}
                        <li class="mb-2">
                            <i class="fas fa-times-circle text-danger me-2"></i>{{ issue }}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-lightbulb fa-fw me-2"></i>Recommendations
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        {% for recommendation in health_status.recommendations %}
                        <li class="mb-2">
                            <i class="fas fa-arrow-right text-info me-2"></i>{{ recommendation }}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- System Information -->
    <div class="row mb-4">
        <div class="col-xl-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock fa-fw me-2"></i>System Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <p><strong>Uptime:</strong> {{ (system_metrics.uptime_seconds // 3600)|int }} hours, {{ ((system_metrics.uptime_seconds % 3600) // 60)|int }} minutes</p>
                            <p><strong>Active Connections:</strong> {{ system_metrics.active_connections }}</p>
                        </div>
                        <div class="col-md-3">
                            <p><strong>Last Health Check:</strong> {{ health_status.last_check }}</p>
                            <p><strong>Health Score:</strong> {{ health_status.score }}/100</p>
                        </div>
                        <div class="col-md-3" id="performance-summary">
                            <!-- Performance summary will be populated here -->
                        </div>
                        <div class="col-md-3" id="optimization-summary">
                            <!-- Optimization summary will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert">
        <div class="toast-header">
            <i class="fas fa-check-circle text-success me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="successMessage"></div>
    </div>
    <div id="errorToast" class="toast" role="alert">
        <div class="toast-header">
            <i class="fas fa-exclamation-circle text-danger me-2"></i>
            <strong class="me-auto">Error</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="errorMessage"></div>
    </div>
</div>

<script>
// Global variables
let refreshInterval;
let performanceData = {};
let optimizationData = {};

// CSRF token helper function
function getCSRFToken() {
    // Try to get CSRF token from meta tag
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        return metaToken.getAttribute('content');
    }

    // Try to get from hidden input field
    const hiddenToken = document.querySelector('input[name="csrf_token"]');
    if (hiddenToken) {
        return hiddenToken.value;
    }

    // Try to get from cookie
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrf_token') {
            return decodeURIComponent(value);
        }
    }

    console.warn('CSRF token not found - this may cause 400 errors on POST requests');
    return null;
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadPerformanceData();
    loadOptimizationData();
    loadBatchStatus();
    startAutoRefresh();
});

function loadPerformanceData() {
    fetch('/admin/health/api/performance-metrics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                performanceData = data.data;
                updatePerformanceDisplay();
                updatePerformanceAlerts();
            } else {
                console.log('Performance monitoring not available:', data.error);
                showPerformanceUnavailable();
            }
        })
        .catch(error => {
            console.error('Error loading performance data:', error);
            showPerformanceUnavailable();
        });
}

function loadOptimizationData() {
    fetch('/admin/health/api/database-optimization')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                optimizationData = data.data;
                updateOptimizationDisplay();
            } else {
                console.log('Database optimization not available:', data.error);
                showOptimizationUnavailable();
            }
        })
        .catch(error => {
            console.error('Error loading optimization data:', error);
            showOptimizationUnavailable();
        });
}

function loadBatchStatus() {
    fetch('/admin/health/api/batch-status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateBatchStatusDisplay(data.data);
            } else {
                console.log('Batch processing not available:', data.error);
                showBatchUnavailable();
            }
        })
        .catch(error => {
            console.error('Error loading batch status:', error);
            showBatchUnavailable();
        });
}

function updatePerformanceDisplay() {
    // Update function performance table
    const tbody = document.querySelector('#function-performance-table tbody');
    tbody.innerHTML = '';

    Object.entries(performanceData.function_statistics).forEach(([funcName, stats]) => {
        const row = tbody.insertRow();
        const errorRate = stats.total_calls > 0 ? (stats.error_count / stats.total_calls * 100) : 0;
        const status = getPerformanceStatus(stats.avg_time, errorRate);

        row.innerHTML = `
            <td><code class="small">${funcName}</code></td>
            <td>${stats.total_calls}</td>
            <td>${stats.avg_time.toFixed(3)}</td>
            <td>${stats.max_time.toFixed(3)}</td>
            <td>${stats.avg_memory.toFixed(2)}</td>
            <td>${stats.error_count}</td>
            <td><span class="badge bg-${status.color}">${status.text}</span></td>
        `;
    });

    // Update performance summary
    const summaryDiv = document.getElementById('performance-summary');
    summaryDiv.innerHTML = `
        <p><strong>Total Functions Monitored:</strong> ${Object.keys(performanceData.function_statistics).length}</p>
        <p><strong>Recent Metrics:</strong> ${performanceData.metrics_count}</p>
    `;
}

function updatePerformanceAlerts() {
    const alertsSection = document.getElementById('performance-alerts');
    const alertsContainer = document.getElementById('alerts-container');

    if (performanceData.bottlenecks && performanceData.bottlenecks.length > 0) {
        alertsSection.style.display = 'block';
        alertsContainer.innerHTML = performanceData.bottlenecks.map(alert => `
            <div class="alert alert-${alert.severity === 'critical' ? 'danger' : 'warning'} alert-dismissible fade show">
                <strong>${alert.severity.toUpperCase()}:</strong> ${alert.message}
                <br><small>Function: ${alert.function_name} | Time: ${alert.execution_time.toFixed(3)}s | Memory: ${alert.memory_usage_mb.toFixed(1)}MB</small>
                ${alert.recommendations ? `<br><small><strong>Recommendations:</strong> ${alert.recommendations.join(', ')}</small>` : ''}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `).join('');
    } else {
        alertsSection.style.display = 'none';
    }
}

function updateOptimizationDisplay() {
    const optimizationPanel = document.getElementById('optimization-panel');

    const suggestionsCount = optimizationData.suggestions ? optimizationData.suggestions.length : 0;
    const indexesCount = optimizationData.indexes ? optimizationData.indexes.length : 0;
    const tablesCount = optimizationData.table_statistics ? optimizationData.table_statistics.length : 0;

    optimizationPanel.innerHTML = `
        <div class="mb-3">
            <h6 class="text-primary">Database Statistics</h6>
            <p class="mb-1"><strong>Tables:</strong> ${tablesCount}</p>
            <p class="mb-1"><strong>Indexes:</strong> ${indexesCount}</p>
            <p class="mb-3"><strong>Optimization Suggestions:</strong> ${suggestionsCount}</p>
        </div>

        ${suggestionsCount > 0 ? `
        <div class="mb-3">
            <h6 class="text-warning">Optimization Opportunities</h6>
            <ul class="list-unstyled small">
                ${optimizationData.suggestions.slice(0, 3).map(suggestion => `
                    <li class="mb-1"><i class="fas fa-arrow-right text-warning me-1"></i>${suggestion.table} (${suggestion.columns.join(', ')})</li>
                `).join('')}
                ${suggestionsCount > 3 ? `<li class="text-muted">... and ${suggestionsCount - 3} more</li>` : ''}
            </ul>
        </div>
        ` : '<div class="alert alert-success small mb-3">All indexes optimized!</div>'}

        <button onclick="optimizeDatabase()" class="btn btn-primary btn-sm w-100">
            <i class="fas fa-tools me-1"></i>Optimize Database
        </button>
    `;

    // Update optimization summary
    const summaryDiv = document.getElementById('optimization-summary');
    summaryDiv.innerHTML = `
        <p><strong>Database Tables:</strong> ${tablesCount}</p>
        <p><strong>Optimization Suggestions:</strong> ${suggestionsCount}</p>
    `;
}

function updateBatchStatusDisplay(batchData) {
    const batchStatusDiv = document.getElementById('batch-status');

    if (batchData.resource_stats) {
        const stats = batchData.resource_stats;
        batchStatusDiv.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <p class="mb-1"><strong>Current CPU:</strong> ${stats.current_cpu.toFixed(1)}%</p>
                    <p class="mb-1"><strong>Avg CPU:</strong> ${stats.avg_cpu.toFixed(1)}%</p>
                    <p class="mb-1"><strong>Max CPU:</strong> ${stats.max_cpu.toFixed(1)}%</p>
                </div>
                <div class="col-6">
                    <p class="mb-1"><strong>Current Memory:</strong> ${stats.current_memory.toFixed(1)}%</p>
                    <p class="mb-1"><strong>Avg Memory:</strong> ${stats.avg_memory.toFixed(1)}%</p>
                    <p class="mb-1"><strong>Max Memory:</strong> ${stats.max_memory.toFixed(1)}%</p>
                </div>
            </div>
            <div class="mt-2">
                <p class="mb-1"><strong>Optimal Workers:</strong> ${stats.optimal_workers}</p>
                <p class="mb-0">
                    <strong>Status:</strong>
                    <span class="badge bg-${stats.should_throttle ? 'warning' : 'success'}">
                        ${stats.should_throttle ? 'Throttling' : 'Normal'}
                    </span>
                </p>
            </div>
        `;
    } else {
        showBatchUnavailable();
    }
}

function showPerformanceUnavailable() {
    document.querySelector('#function-performance-table tbody').innerHTML =
        '<tr><td colspan="7" class="text-center text-muted">Performance monitoring not available</td></tr>';
    document.getElementById('performance-summary').innerHTML =
        '<p class="text-muted">Performance monitoring not available</p>';
}

function showOptimizationUnavailable() {
    document.getElementById('optimization-panel').innerHTML =
        '<p class="text-center text-muted">Database optimization not available</p>';
    document.getElementById('optimization-summary').innerHTML =
        '<p class="text-muted">Optimization data not available</p>';
}

function showBatchUnavailable() {
    document.getElementById('batch-status').innerHTML =
        '<p class="text-center text-muted">Batch processing status not available</p>';
}

function getPerformanceStatus(avgTime, errorRate) {
    if (errorRate > 5 || avgTime > 5) return { color: 'danger', text: 'Poor' };
    if (errorRate > 1 || avgTime > 1) return { color: 'warning', text: 'Fair' };
    return { color: 'success', text: 'Good' };
}

function optimizeDatabase() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Optimizing...';
    button.disabled = true;

    // Get CSRF token
    const csrfToken = getCSRFToken();

    const requestOptions = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    };

    // Add CSRF token if available
    if (csrfToken) {
        requestOptions.headers['X-CSRFToken'] = csrfToken;
    }

    fetch('/admin/health/api/optimize-database', requestOptions)
        .then(response => {
            if (!response.ok) {
                if (response.status === 400) {
                    throw new Error('Bad Request - Check CSRF token and request format');
                } else if (response.status === 401) {
                    throw new Error('Authentication required - Please log in');
                } else if (response.status === 403) {
                    throw new Error('Permission denied - Insufficient privileges');
                } else if (response.status === 503) {
                    throw new Error('Database optimizer not available');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showSuccess(data.data.message);
                loadOptimizationData(); // Reload optimization data
            } else {
                showError(data.error || 'Database optimization failed');
            }
        })
        .catch(error => {
            console.error('Database optimization error:', error);
            showError('Database optimization failed: ' + error.message);
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
}

function refreshMetrics() {
    const icon = document.getElementById('refresh-icon');
    icon.classList.add('fa-spin');

    Promise.all([
        loadPerformanceData(),
        loadOptimizationData(),
        loadBatchStatus()
    ]).finally(() => {
        icon.classList.remove('fa-spin');
        showSuccess('Metrics refreshed successfully');
    });
}

function startAutoRefresh() {
    // Refresh every 30 seconds
    refreshInterval = setInterval(() => {
        loadPerformanceData();
        loadBatchStatus();
    }, 30000);
}

function showSuccess(message) {
    document.getElementById('successMessage').textContent = message;
    new bootstrap.Toast(document.getElementById('successToast')).show();
}

function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    new bootstrap.Toast(document.getElementById('errorToast')).show();
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>
{% endblock %}
